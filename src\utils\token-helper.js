/**
 * Token 处理工具
 * 提供统一的 Token 提取和处理功能
 */

/**
 * 从响应数据中提取 Token
 * @param {Object} data - 响应数据
 * @returns {String|null} - 提取到的 Token 或 null
 */
export function extractToken(data) {
  console.log('extractToken: 开始提取Token，输入数据:', data)

  if (!data) {
    console.log('extractToken: 输入数据为空，返回null')
    return null
  }

  // 1. 直接在顶层查找
  if (data.token) {
    console.log('extractToken: 在顶层找到token字段:', data.token)
    return data.token
  }
  if (data.access_token) {
    console.log('extractToken: 在顶层找到access_token字段:', data.access_token)
    return data.access_token
  }

  // 2. 在 data 字段中查找
  if (data.data) {
    if (data.data.token) {
      console.log('extractToken: 在data.token中找到token:', data.data.token)
      return data.data.token
    }
    if (data.data.access_token) {
      console.log('extractToken: 在data.access_token中找到token:', data.data.access_token)
      return data.data.access_token
    }
  }

  console.log('extractToken: 未找到token，返回null')
  return null
}

/**
 * 从响应数据中提取用户信息
 * @param {Object} data - 响应数据
 * @returns {Object|null} - 提取到的用户信息或 null
 */
export function extractUserInfo(data) {
  console.log('extractUserInfo: 开始提取用户信息，输入数据:', data)

  if (!data) {
    console.log('extractUserInfo: 输入数据为空，返回null')
    return null
  }

  // 1. 直接在顶层查找
  if (data.user) {
    console.log('extractUserInfo: 在顶层找到user字段:', data.user)
    return data.user
  }

  // 2. 在 data 字段中查找
  if (data.data && data.data.user) {
    console.log('extractUserInfo: 在data.user中找到用户信息:', data.data.user)
    return data.data.user
  }

  // 3. 检查是否响应本身就是用户信息
  if (data.username || data.u_name || data.id || data.u_id) {
    console.log('extractUserInfo: 响应本身就是用户信息:', data)
    return data
  }

  console.log('extractUserInfo: 未找到用户信息，返回null')
  return null
}

/**
 * 从响应数据中提取用户ID
 * @param {Object} data - 响应数据
 * @returns {Number|null} - 用户ID
 */
export function extractUserId(data) {
  if (!data) return null

  // 尝试从不同字段获取用户ID
  if (data.user_id) return parseInt(data.user_id)
  if (data.u_id) return parseInt(data.u_id)
  if (data.id) return parseInt(data.id)

  // 如果数据中包含user对象，尝试从user对象中获取
  if (data.user) {
    if (data.user.user_id) return parseInt(data.user.user_id)
    if (data.user.u_id) return parseInt(data.user.u_id)
    if (data.user.id) return parseInt(data.user.id)
  }

  return null
}

/**
 * 从用户信息中提取用户名
 * @param {Object} userInfo - 用户信息
 * @param {String} defaultName - 默认用户名
 * @returns {String} - 提取到的用户名或默认值
 */
export function extractUserName(userInfo, defaultName = '') {
  if (!userInfo) {
    console.log('extractUserName: userInfo为空，返回默认值:', defaultName)
    return defaultName
  }

  console.log('extractUserName: 尝试从用户信息中提取用户名:', userInfo)

  // 按优先级尝试不同的字段名
  const name = userInfo.u_name || userInfo.name || userInfo.username || defaultName

  console.log('extractUserName: 提取到的用户名:', name)
  return name
}

/**
 * 从响应数据中提取用户角色
 * @param {Object} data - 响应数据
 * @returns {Array} - 角色数组
 */
export function extractRoles(data) {
  if (!data) return ['user']

  // 尝试从不同字段获取角色
  let role = null
  if (data.role) role = data.role
  if (data.u_role) role = data.u_role

  // 如果数据中包含user对象，尝试从user对象中获取
  if (data.user) {
    if (data.user.role) role = data.user.role
    if (data.user.u_role) role = data.user.u_role
  }

  // 如果找到角色，返回数组
  if (role) {
    return Array.isArray(role) ? role : [role]
  }

  // 默认返回普通用户角色
  return ['user']
}
