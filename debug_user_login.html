<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录状态调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            color: #409EFF;
            margin-top: 0;
        }
        .info-item {
            margin: 8px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
        }
        .success { color: #67C23A; font-weight: bold; }
        .error { color: #F56C6C; font-weight: bold; }
        .warning { color: #E6A23C; font-weight: bold; }
        button {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #337ecc; }
        .login-form {
            display: flex;
            gap: 10px;
            align-items: center;
            margin: 10px 0;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户登录状态调试页面</h1>
        
        <div class="section">
            <h3>1. 当前登录状态</h3>
            <div id="loginStatus" class="info-item">检查中...</div>
        </div>

        <div class="section">
            <h3>2. 本地存储信息</h3>
            <div id="localStorageInfo" class="info-item">获取中...</div>
        </div>

        <div class="section">
            <h3>3. 用户详细信息</h3>
            <div id="userDetailInfo" class="info-item">获取中...</div>
        </div>

        <div class="section">
            <h3>4. 保安用户登录测试</h3>
            <div class="login-form">
                <input type="text" id="username" placeholder="用户名" value="security">
                <input type="password" id="password" placeholder="密码" value="123456">
                <button onclick="testSecurityLogin()">登录保安账户</button>
            </div>
            <div id="loginResult" class="info-item">等待登录...</div>
        </div>

        <div class="section">
            <h3>5. 权限测试</h3>
            <button onclick="testViolationPageAccess()">测试违规录入页面访问</button>
            <div id="permissionResult" class="info-item">等待测试...</div>
        </div>

        <div class="section">
            <h3>6. 操作</h3>
            <button onclick="refreshAll()">刷新所有信息</button>
            <button onclick="clearStorage()">清除本地存储</button>
        </div>
    </div>

    <script>
        // 获取Token
        function getToken() {
            return localStorage.getItem('vue_admin_template_token') || 
                   sessionStorage.getItem('vue_admin_template_token') ||
                   getCookie('vue_admin_template_token');
        }

        // 获取Cookie
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        // 检查登录状态
        function checkLoginStatus() {
            const statusDiv = document.getElementById('loginStatus');
            const token = getToken();
            
            if (token) {
                statusDiv.innerHTML = `<span class="success">✅ 已登录</span><br>Token: ${token.substring(0, 50)}...`;
            } else {
                statusDiv.innerHTML = '<span class="error">❌ 未登录</span>';
            }
        }

        // 获取本地存储信息
        function getLocalStorageInfo() {
            const infoDiv = document.getElementById('localStorageInfo');
            
            const userId = localStorage.getItem('userId');
            const userName = localStorage.getItem('userName');
            
            let info = '';
            info += `用户ID: ${userId || '未设置'}<br>`;
            info += `用户名: ${userName || '未设置'}<br>`;
            
            // 检查所有相关的localStorage项
            const keys = ['vue_admin_template_token', 'userId', 'userName'];
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                info += `${key}: ${value ? (value.length > 50 ? value.substring(0, 50) + '...' : value) : '未设置'}<br>`;
            });
            
            infoDiv.innerHTML = info;
        }

        // 获取用户详细信息
        async function getUserDetailInfo() {
            const infoDiv = document.getElementById('userDetailInfo');
            
            try {
                const userId = localStorage.getItem('userId');
                const token = getToken();
                
                if (!userId || !token) {
                    infoDiv.innerHTML = '<span class="warning">⚠️ 缺少用户ID或Token</span>';
                    return;
                }
                
                const response = await fetch(`/api/users/${userId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const user = data.data.user || data.data;
                    
                    let info = '';
                    info += `用户ID: ${user.u_id || user.id}<br>`;
                    info += `用户名: ${user.u_name || user.name}<br>`;
                    info += `角色: ${user.u_role || user.role}<br>`;
                    info += `状态: ${user.u_status || user.status}<br>`;
                    info += `所属: ${user.u_belong || user.belong || '未设置'}<br>`;
                    
                    infoDiv.innerHTML = info;
                } else {
                    infoDiv.innerHTML = `<span class="error">❌ 获取用户信息失败 (${response.status})</span>`;
                }
            } catch (error) {
                infoDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            }
        }

        // 测试保安用户登录
        async function testSecurityLogin() {
            const resultDiv = document.getElementById('loginResult');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            resultDiv.innerHTML = '登录中...';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 保存Token和用户信息
                    if (data.data && data.data.access_token) {
                        localStorage.setItem('vue_admin_template_token', data.data.access_token);
                        
                        if (data.data.user) {
                            localStorage.setItem('userId', data.data.user.u_id || data.data.user.id);
                            localStorage.setItem('userName', data.data.user.u_name || data.data.user.name);
                        }
                        
                        resultDiv.innerHTML = '<span class="success">✅ 登录成功</span>';
                        
                        // 刷新其他信息
                        setTimeout(() => {
                            refreshAll();
                        }, 1000);
                    } else {
                        resultDiv.innerHTML = '<span class="error">❌ 登录响应格式错误</span>';
                    }
                } else {
                    const errorData = await response.json();
                    resultDiv.innerHTML = `<span class="error">❌ 登录失败: ${errorData.message || response.statusText}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 登录错误: ${error.message}</span>`;
            }
        }

        // 测试违规录入页面访问
        function testViolationPageAccess() {
            const resultDiv = document.getElementById('permissionResult');
            
            // 打开违规录入页面
            const newWindow = window.open('/violation/security/create', '_blank');
            
            if (newWindow) {
                resultDiv.innerHTML = '<span class="success">✅ 页面已打开，请检查是否显示权限错误</span>';
            } else {
                resultDiv.innerHTML = '<span class="error">❌ 无法打开页面，可能被浏览器阻止</span>';
            }
        }

        // 刷新所有信息
        function refreshAll() {
            checkLoginStatus();
            getLocalStorageInfo();
            getUserDetailInfo();
        }

        // 清除本地存储
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            
            alert('本地存储已清除');
            refreshAll();
        }

        // 页面加载时自动检查
        window.onload = function() {
            refreshAll();
        };
    </script>
</body>
</html>
