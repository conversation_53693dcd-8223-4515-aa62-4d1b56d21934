<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户权限检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .info-section h3 {
            color: #409EFF;
            margin-top: 0;
        }
        .info-item {
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        .success {
            color: #67C23A;
            font-weight: bold;
        }
        .error {
            color: #F56C6C;
            font-weight: bold;
        }
        .warning {
            color: #E6A23C;
            font-weight: bold;
        }
        button {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #337ecc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户权限检查页面</h1>
        <p>此页面用于检查当前用户的登录状态和权限信息</p>

        <div class="info-section">
            <h3>1. 登录状态检查</h3>
            <div id="loginStatus" class="info-item">检查中...</div>
        </div>

        <div class="info-section">
            <h3>2. 用户基本信息</h3>
            <div id="userInfo" class="info-item">获取中...</div>
        </div>

        <div class="info-section">
            <h3>3. 权限信息</h3>
            <div id="permissionInfo" class="info-item">检查中...</div>
        </div>

        <div class="info-section">
            <h3>4. 违规录入页面权限检查</h3>
            <div id="violationPermission" class="info-item">检查中...</div>
        </div>

        <div class="info-section">
            <h3>5. 操作</h3>
            <button onclick="refreshInfo()">刷新信息</button>
            <button onclick="testViolationPage()">测试违规录入页面</button>
        </div>
    </div>

    <script>
        // 检查登录状态
        function checkLoginStatus() {
            const token = localStorage.getItem('vue_admin_template_token') || 
                         sessionStorage.getItem('vue_admin_template_token') ||
                         document.cookie.split(';').find(c => c.trim().startsWith('vue_admin_template_token='));
            
            const statusDiv = document.getElementById('loginStatus');
            
            if (token) {
                statusDiv.innerHTML = '<span class="success">✅ 已登录</span><br>Token: ' + (token.length > 50 ? token.substring(0, 50) + '...' : token);
            } else {
                statusDiv.innerHTML = '<span class="error">❌ 未登录</span>';
            }
            
            return !!token;
        }

        // 获取用户信息
        function getUserInfo() {
            const userInfoDiv = document.getElementById('userInfo');
            
            // 从localStorage获取用户信息
            const userId = localStorage.getItem('userId');
            const userName = localStorage.getItem('userName');
            
            let info = '';
            if (userId) {
                info += `用户ID: ${userId}<br>`;
            }
            if (userName) {
                info += `用户名: ${userName}<br>`;
            }
            
            if (!info) {
                info = '<span class="warning">⚠️ 未找到本地用户信息</span>';
            }
            
            userInfoDiv.innerHTML = info;
        }

        // 检查权限信息
        async function checkPermissions() {
            const permissionDiv = document.getElementById('permissionInfo');
            
            try {
                // 尝试获取用户详细信息
                const userId = localStorage.getItem('userId');
                if (!userId) {
                    permissionDiv.innerHTML = '<span class="error">❌ 无法获取用户ID</span>';
                    return;
                }
                
                const response = await fetch(`/api/users/${userId}`, {
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('vue_admin_template_token') || ''),
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const user = data.data.user || data.data;
                    
                    let permissionInfo = '';
                    permissionInfo += `角色: ${user.u_role || user.role || '未知'}<br>`;
                    permissionInfo += `状态: ${user.u_status || user.status || '未知'}<br>`;
                    
                    // 检查是否是保安或管理员
                    const role = user.u_role || user.role;
                    if (role === 'security') {
                        permissionInfo += '<span class="success">✅ 保安权限</span><br>';
                    } else if (role === 'admin') {
                        permissionInfo += '<span class="success">✅ 管理员权限</span><br>';
                    } else {
                        permissionInfo += '<span class="warning">⚠️ 普通用户权限</span><br>';
                    }
                    
                    permissionDiv.innerHTML = permissionInfo;
                } else {
                    permissionDiv.innerHTML = `<span class="error">❌ 获取用户信息失败 (${response.status})</span>`;
                }
            } catch (error) {
                permissionDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            }
        }

        // 检查违规录入页面权限
        async function checkViolationPermission() {
            const violationDiv = document.getElementById('violationPermission');
            
            try {
                // 尝试访问违规录入页面的API
                const response = await fetch('/api/violations/type-options', {
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('vue_admin_template_token') || ''),
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    violationDiv.innerHTML = '<span class="success">✅ 可以访问违规录入相关API</span><br>违规类型数量: ' + (data.data?.data?.length || 0);
                } else {
                    violationDiv.innerHTML = `<span class="error">❌ 无法访问违规录入API (${response.status})</span>`;
                }
            } catch (error) {
                violationDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            }
        }

        // 刷新所有信息
        function refreshInfo() {
            checkLoginStatus();
            getUserInfo();
            checkPermissions();
            checkViolationPermission();
        }

        // 测试违规录入页面
        function testViolationPage() {
            window.open('/violation/security/create', '_blank');
        }

        // 页面加载时自动检查
        window.onload = function() {
            refreshInfo();
        };
    </script>
</body>
</html>
