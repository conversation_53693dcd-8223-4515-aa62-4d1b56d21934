from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.violations import violations_bp
from app.violations.models import ViolationRecord, Appeal, Evidence, ViolationType, VehicleDisableRecord
from app.violations.schema import ViolationRecordSchema, AppealSchema, EvidenceSchema, ViolationTypeSchema
from app.utils.auth_helpers import get_current_user_id, is_admin
from app.utils.validation import admin_required, admin_or_security_required
from app.utils.response import api_response
from app.utils.paginate import paginate_data
from app.utils.vehicle_status import is_vehicle_disabled
from app.tasks.check_vehicle_disable import disable_vehicle, enable_vehicle
from app.utils.violation_status import ViolationStatus, AppealStatus, create_appeal, handle_appeal as process_appeal
from marshmallow import ValidationError
from datetime import datetime, timedelta
import os
import uuid
from werkzeug.utils import secure_filename
from app.bikes.models import Bikes

# 获取当前用户的违规记录
@violations_bp.route('/user/records', methods=['GET'])
@jwt_required()
def get_user_violations():
    """获取当前用户的违规记录"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取查询参数
        status = request.args.get('status')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # 构建查询
        query = ViolationRecord.query.filter_by(user_id=user_id)

        # 按状态筛选
        if status is not None:
            query = query.filter_by(status=int(status))

        # 按ID升序排序
        query = query.order_by(ViolationRecord.id.asc())

        # 分页
        paginated_data = paginate_data(query, page, per_page)

        # 序列化数据
        schema = ViolationRecordSchema(many=True)
        result = paginated_data
        result['items'] = schema.dump(paginated_data['items'])

        return api_response(data=result, message="获取违规记录成功", status="success")
    except Exception as e:
        current_app.logger.error(f"获取用户违规记录失败: {str(e)}")
        return api_response(message=f"获取违规记录失败: {str(e)}", status="error", code=500)

# 获取违规记录详情
@violations_bp.route('/records/<int:violation_id>', methods=['GET'])
@jwt_required()
def get_violation_detail(violation_id):
    """获取违规记录详情"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取违规记录
        violation = ViolationRecord.query.get(violation_id)
        if not violation:
            return api_response(message="违规记录不存在", status="error", code=404)

        # 权限检查：只有管理员、记录创建者和违规记录所有者可以查看
        if not is_admin() and user_id != violation.user_id and user_id != violation.recorder_id:
            return api_response(message="没有权限查看此违规记录", status="error", code=403)

        # 获取关联的申诉记录
        appeals = Appeal.query.filter_by(violation_id=violation_id).order_by(Appeal.created_at.desc()).all()

        # 获取关联的证据
        evidences = Evidence.query.filter_by(related_id=violation_id, related_type='violation').all()

        # 序列化数据
        violation_schema = ViolationRecordSchema()
        appeal_schema = AppealSchema(many=True)
        evidence_schema = EvidenceSchema(many=True)

        result = {
            'violation': violation_schema.dump(violation),
            'appeals': appeal_schema.dump(appeals),
            'evidences': evidence_schema.dump(evidences)
        }

        return api_response(data=result, message="获取违规记录详情成功", status="success")
    except Exception as e:
        current_app.logger.error(f"获取违规记录详情失败: {str(e)}")
        return api_response(message=f"获取违规记录详情失败: {str(e)}", status="error", code=500)

# 提交申诉
@violations_bp.route('/appeals', methods=['POST'])
@jwt_required()
def submit_appeal():
    """提交申诉"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取请求数据
        data = request.get_json()
        if not data:
            return api_response(message="请求数据为空", status="error", code=400)

        # 获取违规记录ID和申诉理由
        violation_id = data.get('violation_id')
        reason = data.get('reason')

        if not violation_id:
            return api_response(message="缺少违规记录ID", status="error", code=400)

        if not reason:
            return api_response(message="缺少申诉理由", status="error", code=400)

        # 使用统一的申诉创建函数
        success, message, appeal = create_appeal(
            violation_id=violation_id,
            user_id=user_id,
            reason=reason
        )

        if not success:
            return api_response(message=message, status="error", code=400)

        # 序列化数据
        schema = AppealSchema()
        return api_response(data=schema.dump(appeal), message=message, status="success")
    except Exception as e:
        current_app.logger.error(f"提交申诉失败: {str(e)}")
        return api_response(message=f"提交申诉失败: {str(e)}", status="error", code=500)

# 上传证据
@violations_bp.route('/evidences', methods=['POST'])
@jwt_required()
def upload_evidence():
    """上传证据"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 检查是否有文件上传
        if 'file' not in request.files:
            return api_response(message="没有上传文件", status="error", code=400)

        file = request.files['file']
        if file.filename == '':
            return api_response(message="未选择文件", status="error", code=400)

        # 获取关联ID和类型
        related_id = request.form.get('related_id')
        related_type = request.form.get('related_type')

        if not related_id or not related_type:
            return api_response(message="缺少关联ID或类型", status="error", code=400)

        # 验证关联类型
        if related_type not in ['violation', 'appeal']:
            return api_response(message="无效的关联类型", status="error", code=400)

        # 验证关联对象是否存在
        if related_type == 'violation':
            related_obj = ViolationRecord.query.get(related_id)
            if not related_obj:
                return api_response(message="违规记录不存在", status="error", code=404)
            # 权限检查：只有管理员、记录创建者和违规记录所有者可以上传证据
            if not is_admin() and user_id != related_obj.user_id and user_id != related_obj.recorder_id:
                return api_response(message="没有权限为此违规记录上传证据", status="error", code=403)
        else:  # appeal
            related_obj = Appeal.query.get(related_id)
            if not related_obj:
                return api_response(message="申诉记录不存在", status="error", code=404)
            # 权限检查：只有管理员和申诉创建者可以上传证据
            if not is_admin() and user_id != related_obj.user_id:
                return api_response(message="没有权限为此申诉上传证据", status="error", code=403)

        # 确定文件类型
        filename = file.filename
        file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

        if file_ext in ['jpg', 'jpeg', 'png', 'gif']:
            evidence_type = 'image'
        elif file_ext in ['mp4', 'avi', 'mov']:
            evidence_type = 'video'
        else:
            return api_response(message="不支持的文件类型", status="error", code=400)

        # 创建上传目录
        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'evidences', related_type)
        os.makedirs(upload_dir, exist_ok=True)

        # 生成唯一文件名
        import uuid
        unique_filename = f"{uuid.uuid4().hex}.{file_ext}"
        file_path = os.path.join(upload_dir, unique_filename)

        # 保存文件
        file.save(file_path)

        # 创建证据记录
        evidence_data = {
            'related_id': int(related_id),
            'related_type': related_type,
            'evidence_type': evidence_type,
            'file_path': f"/uploads/evidences/{related_type}/{unique_filename}",
            'uploader_id': user_id
        }

        schema = EvidenceSchema()
        evidence = schema.load(evidence_data)

        # 保存到数据库
        db.session.add(evidence)
        db.session.commit()

        return api_response(data=schema.dump(evidence), message="证据上传成功", status="success")
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"上传证据失败: {str(e)}")
        return api_response(message=f"上传证据失败: {str(e)}", status="error", code=500)

# 保安：创建违规记录
@violations_bp.route('/records', methods=['POST'])
@jwt_required()
def create_violation():
    """创建违规记录"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取请求数据
        data = request.get_json()
        current_app.logger.info(f"接收到的违规记录数据: {data}")

        if not data:
            return api_response(message="请求数据为空", status="error", code=400)

        # 检查必填字段
        required_fields = ['bike_number', 'user_id', 'violation_type', 'violation_time', 'location']
        missing_fields = [field for field in required_fields if field not in data or not data[field]]
        if missing_fields:
            return api_response(
                message=f"缺少必填字段: {', '.join(missing_fields)}",
                status="error",
                code=400
            )

        # 确保 user_id 是整数
        if 'user_id' in data and isinstance(data['user_id'], str):
            try:
                data['user_id'] = int(data['user_id'])
            except ValueError:
                return api_response(message="车主ID必须是数字", status="error", code=400)

        # 处理 violation_time
        violation_time = data.get('violation_time')
        if isinstance(violation_time, str):
            try:
                violation_time = datetime.strptime(violation_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                violation_time = datetime.now()
        elif not violation_time:
            violation_time = datetime.now()

        # 处理 recorder_id，确保使用当前登录用户ID
        recorder_id = user_id
        if data.get('recorder'):
            try:
                recorder_id = int(data.get('recorder'))
            except (ValueError, TypeError):
                # 如果转换失败，使用当前用户ID
                recorder_id = user_id

        try:
            # 直接创建记录
            violation = ViolationRecord()
            violation.bike_number = data.get('bike_number')
            violation.user_id = data.get('user_id')
            violation.violation_time = violation_time
            violation.location = data.get('location')
            violation.violation_type = data.get('violation_type')
            violation.description = data.get('description')
            violation.recorder_id = recorder_id
            violation.status = 0
            violation.bike_id = data.get('bike_id')

            # 保存到数据库
            db.session.add(violation)
            db.session.commit()

            # 构建响应数据
            result = {
                'id': violation.id,
                'bike_number': violation.bike_number,
                'user_id': violation.user_id,
                'violation_time': str(violation.violation_time),
                'location': violation.location,
                'violation_type': violation.violation_type,
                'description': violation.description,
                'status': violation.status,
                'recorder_id': violation.recorder_id,
                'created_at': str(violation.created_at)
            }

            current_app.logger.info(f"违规记录创建成功: {result}")
            return api_response(data=result, message="违规记录创建成功", status="success")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建违规记录实例失败: {str(e)}")
            return api_response(
                message=f"创建违规记录失败: {str(e)}",
                status="error",
                code=400
            )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建违规记录失败: {str(e)}")
        return api_response(message=f"创建违规记录失败: {str(e)}", status="error", code=500)

# 保安：获取自己创建的违规记录
@violations_bp.route('/security/records', methods=['GET'])
@jwt_required()
def get_security_violations():
    """获取保安创建的违规记录"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取查询参数
        status = request.args.get('status')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # 构建查询
        query = ViolationRecord.query.filter_by(recorder_id=user_id)

        # 按状态筛选
        if status is not None:
            query = query.filter_by(status=int(status))

        # 按ID升序排序
        query = query.order_by(ViolationRecord.id.asc())

        # 分页
        paginated_data = paginate_data(query, page, per_page)

        # 序列化数据
        schema = ViolationRecordSchema(many=True)
        result = paginated_data
        result['items'] = schema.dump(paginated_data['items'])

        return api_response(data=result, message="获取违规记录成功", status="success")
    except Exception as e:
        current_app.logger.error(f"获取保安违规记录失败: {str(e)}")
        return api_response(message=f"获取违规记录失败: {str(e)}", status="error", code=500)

# 管理员：获取所有违规记录
@violations_bp.route('/admin/records', methods=['GET'])
@jwt_required()
@admin_required
def get_all_violations():
    """获取所有违规记录（管理员）"""
    try:
        # 获取查询参数
        status = request.args.get('status')
        user_id = request.args.get('user_id')
        recorder_id = request.args.get('recorder_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 15, type=int)
        sort = request.args.get('sort', 'id')
        order = request.args.get('order', 'asc')
        violation_id = request.args.get('id')
        violation_type = request.args.get('violation_type')
        exclude_status = request.args.get('exclude_status')

        # 打印调试信息
        current_app.logger.info(f"管理员违规记录查询参数: status={status}, exclude_status={exclude_status}, user_id={user_id}, "
                               f"recorder_id={recorder_id}, sort={sort}, order={order}")

        # 构建查询
        query = ViolationRecord.query

        # 应用筛选条件
        if status is not None:
            query = query.filter_by(status=int(status))
        # 排除特定状态的记录
        if exclude_status is not None:
            query = query.filter(ViolationRecord.status != int(exclude_status))
        if user_id:
            query = query.filter_by(user_id=int(user_id))
        if recorder_id:
            query = query.filter_by(recorder_id=int(recorder_id))
        if violation_id:
            try:
                query = query.filter_by(id=int(violation_id))
            except ValueError:
                current_app.logger.error(f"Invalid violation_id format: {violation_id}")
        if violation_type:
            query = query.filter_by(violation_type=violation_type)
        # 按处理人筛选
        handler_id = request.args.get('handler_id')
        if handler_id:
            try:
                query = query.filter_by(handler_id=int(handler_id))
            except ValueError:
                current_app.logger.error(f"Invalid handler_id format: {handler_id}")
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(ViolationRecord.violation_time >= start_datetime)
            except ValueError:
                current_app.logger.error(f"Invalid start_date format: {start_date}")
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                query = query.filter(ViolationRecord.violation_time <= end_datetime)
            except ValueError:
                current_app.logger.error(f"Invalid end_date format: {end_date}")

        # 根据排序参数排序
        if sort == 'id':
            if order == 'asc':
                query = query.order_by(ViolationRecord.id.asc())
            else:
                query = query.order_by(ViolationRecord.id.desc())
        elif sort == 'violation_time':
            if order == 'asc':
                query = query.order_by(ViolationRecord.violation_time.asc())
            else:
                query = query.order_by(ViolationRecord.violation_time.desc())
        else:
            # 默认按ID升序排序
            query = query.order_by(ViolationRecord.id.asc())

        # 分页
        paginated_data = paginate_data(query, page, per_page)

        # 序列化数据
        schema = ViolationRecordSchema(many=True)
        result = paginated_data
        result['items'] = schema.dump(paginated_data['items'])

        return api_response(data=result, message="获取违规记录成功", status="success")
    except Exception as e:
        current_app.logger.error(f"获取所有违规记录失败: {str(e)}")
        return api_response(message=f"获取违规记录失败: {str(e)}", status="error", code=500)

# 管理员：删除违规记录
@violations_bp.route('/admin/records/<int:violation_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_violation(violation_id):
    """删除违规记录（管理员）"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取违规记录
        violation = ViolationRecord.query.get(violation_id)
        if not violation:
            return api_response(message="违规记录不存在", status="error", code=404)

        # 获取关联的申诉记录
        appeals = Appeal.query.filter_by(violation_id=violation_id).all()

        # 删除关联的申诉记录的证据
        for appeal in appeals:
            Evidence.query.filter_by(related_id=appeal.id, related_type='appeal').delete()

        # 删除关联的申诉记录
        Appeal.query.filter_by(violation_id=violation_id).delete()

        # 删除违规记录关联的证据
        Evidence.query.filter_by(related_id=violation_id, related_type='violation').delete()

        # 处理关联的车辆禁用记录
        # 方法1：删除关联的车辆禁用记录
        VehicleDisableRecord.query.filter_by(violation_id=violation_id).delete()

        # 方法2：将关联的车辆禁用记录的violation_id设为NULL（如果violation_id允许为NULL）
        # disable_records = VehicleDisableRecord.query.filter_by(violation_id=violation_id).all()
        # for record in disable_records:
        #     record.violation_id = None
        #     record.reason = "原关联违规记录已删除"

        # 删除违规记录
        db.session.delete(violation)
        db.session.commit()

        return api_response(message="违规记录删除成功", status="success")
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除违规记录失败: {str(e)}")
        return api_response(message=f"删除违规记录失败: {str(e)}", status="error", code=500)

# 管理员：处理违规记录
@violations_bp.route('/admin/records/<int:violation_id>', methods=['PUT'])
@jwt_required()
@admin_required
def handle_violation(violation_id):
    """处理违规记录（管理员）"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取违规记录
        violation = ViolationRecord.query.get(violation_id)
        if not violation:
            return api_response(message="违规记录不存在", status="error", code=404)

        # 获取请求数据
        data = request.get_json()
        if not data:
            return api_response(message="请求数据为空", status="error", code=400)

        # 更新处理人ID
        data['handler_id'] = user_id

        # 记录原始状态，用于后续判断状态变化
        original_status = violation.status

        # 处理日期时间字段
        if 'violation_time' in data and isinstance(data['violation_time'], str):
            try:
                data['violation_time'] = datetime.strptime(data['violation_time'], '%Y-%m-%d %H:%M:%S')
            except ValueError:
                current_app.logger.error(f"Invalid violation_time format: {data['violation_time']}")
                return api_response(message=f"违规时间格式无效: {data['violation_time']}", status="error", code=400)

        # 处理handling_time字段
        if 'handling_time' in data and isinstance(data['handling_time'], str):
            try:
                data['handling_time'] = datetime.strptime(data['handling_time'], '%Y-%m-%d %H:%M:%S')
            except ValueError:
                current_app.logger.error(f"Invalid handling_time format: {data['handling_time']}")
                # 如果处理时间格式无效，使用当前时间
                data['handling_time'] = datetime.now()

        # 更新违规记录
        for key, value in data.items():
            if hasattr(violation, key):
                setattr(violation, key, value)

        # 处理禁用车辆逻辑
        should_disable_vehicle = data.get('disable_vehicle', False)
        disable_result = None

        if should_disable_vehicle and violation.status == 1 and violation.bike_id:  # 只有在状态为"已处理"且有车辆ID时才禁用车辆
            try:
                # 检查车辆是否已被禁用
                is_disabled, reason, _ = is_vehicle_disabled(violation.bike_id)

                if is_disabled:
                    disable_result = {
                        "success": True,
                        "message": f"车辆已经处于禁用状态，原因: {reason}",
                        "already_disabled": True
                    }
                    current_app.logger.info(f"车辆 ID: {violation.bike_id} 已经处于禁用状态，原因: {reason}")
                else:
                    # 使用统一的禁用函数
                    success, message, record_id = disable_vehicle(
                        bike_id=violation.bike_id,
                        violation_id=violation.id,
                        reason=f"违规原因: {violation.violation_type}, 地点: {violation.location}, 时间: {violation.violation_time}",
                        operator_id=user_id
                    )

                    disable_result = {
                        "success": success,
                        "message": message,
                        "record_id": record_id
                    }

                    if success:
                        current_app.logger.info(message)
                    else:
                        current_app.logger.warning(message)
                        # 如果禁用失败且前端要求在禁用失败时返回错误，则回滚事务并返回错误
                        if data.get('fail_on_disable_error', False):
                            db.session.rollback()
                            return api_response(
                                message=f"处理违规记录成功，但禁用车辆失败: {message}",
                                status="error",
                                code=400,
                                data={"disable_result": disable_result}
                            )
            except Exception as disable_error:
                error_message = str(disable_error)
                current_app.logger.error(f"禁用车辆失败: {error_message}")
                disable_result = {
                    "success": False,
                    "message": f"禁用车辆时发生错误: {error_message}"
                }
                # 如果禁用失败且前端要求在禁用失败时返回错误，则回滚事务并返回错误
                if data.get('fail_on_disable_error', False):
                    db.session.rollback()
                    return api_response(
                        message=f"处理违规记录成功，但禁用车辆失败: {error_message}",
                        status="error",
                        code=400,
                        data={"disable_result": disable_result}
                    )

        # 保存到数据库
        db.session.commit()

        # 如果需要通知用户且用户ID存在
        notify_user = data.get('notify_user', False)
        if notify_user and violation.user_id:
            try:
                # 获取状态文本
                status_text = violation.get_status_text()

                # 构建通知消息
                if violation.status == 1:  # 已处理
                    message = f"您的违规记录(ID:{violation.id})已被处理，处理结果：{violation.result or '已确认违规'}。"

                    # 如果车辆被禁用，添加禁用信息
                    if should_disable_vehicle and violation.bike_id and (disable_result and disable_result.get("success", False)):
                        message += f" 您的车辆已被禁用。"

                elif violation.status == 3:  # 已撤销
                    message = f"您的违规记录(ID:{violation.id})已被撤销，撤销原因：{violation.result or '违规记录无效'}。"

                # TODO: 发送通知给用户
                # 这里可以调用通知服务发送消息
                current_app.logger.info(f"向用户{violation.user_id}发送违规处理通知: {message}")

            except Exception as notify_error:
                current_app.logger.error(f"发送通知失败: {str(notify_error)}")
                # 通知失败不影响主流程

        # 序列化数据
        schema = ViolationRecordSchema()
        response_data = schema.dump(violation)

        # 将禁用结果添加到响应中
        if disable_result:
            response_data['disable_result'] = disable_result

        return api_response(data=response_data, message="违规记录处理成功", status="success")
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"处理违规记录失败: {str(e)}")
        return api_response(message=f"处理违规记录失败: {str(e)}", status="error", code=500)

# 管理员：处理申诉
@violations_bp.route('/admin/appeals/<int:appeal_id>', methods=['PUT'])
@jwt_required()
@admin_required
def handle_appeal(appeal_id):
    """处理申诉（管理员）"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取请求数据
        data = request.get_json()
        if not data:
            return api_response(message="请求数据为空", status="error", code=400)

        # 获取申诉状态和处理意见
        status = data.get('status')
        comment = data.get('comment')
        notify_user = data.get('notify_user', False)

        if status not in [AppealStatus.APPROVED, AppealStatus.REJECTED]:
            return api_response(message="无效的申诉状态", status="error", code=400)

        if not comment:
            return api_response(message="处理意见不能为空", status="error", code=400)

        # 使用统一的申诉处理函数
        success, message, appeal = process_appeal(
            appeal_id=appeal_id,
            status=status,
            comment=comment,
            handler_id=user_id,
            notify_user=notify_user
        )

        if not success:
            return api_response(message=message, status="error", code=400)

        # 序列化数据
        schema = AppealSchema()
        return api_response(data=schema.dump(appeal), message=message, status="success")
    except Exception as e:
        current_app.logger.error(f"处理申诉失败: {str(e)}")
        return api_response(message=f"处理申诉失败: {str(e)}", status="error", code=500)

# 管理员：删除申诉记录
@violations_bp.route('/admin/appeals/<int:appeal_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_appeal(appeal_id):
    """删除申诉记录（管理员）"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取申诉记录
        appeal = Appeal.query.get(appeal_id)
        if not appeal:
            return api_response(message="申诉记录不存在", status="error", code=404)

        # 获取关联的违规记录
        violation = ViolationRecord.query.get(appeal.violation_id)
        if violation and violation.status == ViolationStatus.APPEALING:
            # 如果违规记录状态为"申诉中"，则将其恢复为"已处理"状态
            violation.status = ViolationStatus.PROCESSED
            violation.updated_at = datetime.now()

        # 删除关联的证据
        Evidence.query.filter_by(related_id=appeal_id, related_type='appeal').delete()

        # 删除申诉记录
        db.session.delete(appeal)
        db.session.commit()

        return api_response(message="申诉记录删除成功", status="success")
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除申诉记录失败: {str(e)}")
        return api_response(message=f"删除申诉记录失败: {str(e)}", status="error", code=500)

# 管理员：获取所有申诉
@violations_bp.route('/admin/appeals', methods=['GET'])
@jwt_required()
@admin_required
def get_all_appeals():
    """获取所有申诉（管理员）"""
    try:
        # 获取查询参数
        status = request.args.get('status')
        user_id = request.args.get('user_id')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # 构建查询
        query = Appeal.query

        # 应用筛选条件
        if status is not None:
            query = query.filter_by(status=int(status))
        if user_id:
            query = query.filter_by(user_id=int(user_id))

        # 获取排序参数
        sort = request.args.get('sort', 'id')
        order = request.args.get('order', 'asc')

        # 按ID升序排序（默认）
        if sort == 'id':
            if order == 'asc':
                query = query.order_by(Appeal.id.asc())
            else:
                query = query.order_by(Appeal.id.desc())
        # 按创建时间排序
        elif sort == 'created_at':
            if order == 'asc':
                query = query.order_by(Appeal.created_at.asc())
            else:
                query = query.order_by(Appeal.created_at.desc())
        else:
            # 默认按ID升序排序
            query = query.order_by(Appeal.id.asc())

        # 分页
        paginated_data = paginate_data(query, page, per_page)

        # 序列化数据
        schema = AppealSchema(many=True)
        result = paginated_data
        result['items'] = schema.dump(paginated_data['items'])

        return api_response(data=result, message="获取申诉记录成功", status="success")
    except Exception as e:
        current_app.logger.error(f"获取所有申诉记录失败: {str(e)}")
        return api_response(message=f"获取申诉记录失败: {str(e)}", status="error", code=500)

# 获取违规类型选项
@violations_bp.route('/type-options', methods=['GET'])
def get_violation_type_options():
    """获取违规类型列表"""
    try:
        # 违规类型列表
        violation_types = [
            {"value": "违规停车", "label": "违规停车"},
            {"value": "占用消防通道", "label": "占用消防通道"},
            {"value": "占用无障碍通道", "label": "占用无障碍通道"},
            {"value": "超时停车", "label": "超时停车"},
            {"value": "车辆损坏公物", "label": "车辆损坏公物"},
            {"value": "无证驾驶", "label": "无证驾驶"},
            {"value": "其他违规", "label": "其他违规"}
        ]

        return api_response(data=violation_types, message="获取违规类型成功", status="success")
    except Exception as e:
        current_app.logger.error(f"获取违规类型失败: {str(e)}")
        return api_response(message=f"获取违规类型失败: {str(e)}", status="error", code=500)

# 获取公共违规记录
@violations_bp.route('/public/records', methods=['GET'])
def get_public_violations():
    """获取公共违规记录"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 15, type=int)
        search = request.args.get('search', '')
        sort = request.args.get('sort', 'id')
        order = request.args.get('order', 'asc')
        status = request.args.get('status', type=int)
        violation_type = request.args.get('violation_type')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        record_id = request.args.get('id', type=int)

        # 打印调试信息
        current_app.logger.info(f"公共违规记录查询参数: page={page}, per_page={per_page}, search={search}, sort={sort}, order={order}, "
                               f"status={status}, violation_type={violation_type}, start_date={start_date}, end_date={end_date}, id={record_id}")

        # 构建查询
        query = ViolationRecord.query

        # 按ID筛选
        if record_id is not None:
            query = query.filter(ViolationRecord.id == record_id)

        # 按状态筛选
        if status is not None:
            query = query.filter(ViolationRecord.status == status)

        # 按违规类型筛选
        if violation_type:
            query = query.filter(ViolationRecord.violation_type == violation_type)

        # 按日期范围筛选
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(ViolationRecord.violation_time >= start_datetime)
            except ValueError:
                current_app.logger.error(f"Invalid start_date format: {start_date}")

        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                query = query.filter(ViolationRecord.violation_time <= end_datetime)
            except ValueError:
                current_app.logger.error(f"Invalid end_date format: {end_date}")

        # 搜索功能
        if search:
            query = query.filter(
                db.or_(
                    ViolationRecord.bike_number.like(f'%{search}%'),
                    ViolationRecord.location.like(f'%{search}%'),
                    ViolationRecord.violation_type.like(f'%{search}%')
                )
            )

        # 根据排序参数排序
        if sort == 'id':
            if order == 'asc':
                query = query.order_by(ViolationRecord.id.asc())
            else:
                query = query.order_by(ViolationRecord.id.desc())
        elif sort == 'violation_time':
            if order == 'asc':
                query = query.order_by(ViolationRecord.violation_time.asc())
            else:
                query = query.order_by(ViolationRecord.violation_time.desc())
        else:
            # 默认按ID升序排序
            query = query.order_by(ViolationRecord.id.asc())

        # 限制查询结果数量，防止数据量过大
        total_count = query.count()
        current_app.logger.info(f"公共违规记录总数: {total_count}")

        # 计算最大页数
        max_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 1

        # 检查请求的页码是否超出范围
        if page > max_pages:
            current_app.logger.warning(f"请求的页码 {page} 超出了最大页数 {max_pages}，返回空结果")
            empty_result = {'items': [], 'total': total_count, 'page': page, 'per_page': per_page, 'pages': max_pages}
            return api_response(data=empty_result, message="获取公共违规记录成功", status="success")

        # 分页
        try:
            paginated_data = paginate_data(query, page, per_page)
            current_app.logger.info(f"分页数据: {paginated_data}")

            # 序列化数据
            schema = ViolationRecordSchema(many=True)
            result = paginated_data.copy() if paginated_data else {'items': [], 'total': total_count, 'page': page, 'per_page': per_page, 'pages': max_pages}

            if paginated_data and 'items' in paginated_data and paginated_data['items']:
                result['items'] = schema.dump(paginated_data['items'])
            else:
                result['items'] = []

            return api_response(data=result, message="获取公共违规记录成功", status="success")
        except Exception as e:
            current_app.logger.error(f"分页或序列化失败: {str(e)}")
            # 返回空结果
            empty_result = {'items': [], 'total': total_count, 'page': page, 'per_page': per_page, 'pages': max_pages}
            return api_response(data=empty_result, message="获取公共违规记录成功", status="success")
    except Exception as e:
        current_app.logger.error(f"获取公共违规记录失败: {str(e)}")
        # 返回空结果而不是错误状态，避免前端显示错误
        empty_result = {'items': [], 'total': 0, 'page': 1, 'per_page': 15}
        return api_response(data=empty_result, message="获取公共违规记录成功", status="success")

# 获取违规类型列表
@violations_bp.route('/types', methods=['GET'])
def get_violation_types():
    """获取违规类型列表"""
    try:
        # 获取所有违规类型
        types = ViolationType.query.all()

        # 序列化数据
        schema = ViolationTypeSchema(many=True)
        result = schema.dump(types)

        return api_response(data=result, message="获取违规类型列表成功", status="success")
    except Exception as e:
        current_app.logger.error(f"获取违规类型列表失败: {str(e)}")
        return api_response(message=f"获取违规类型列表失败: {str(e)}", status="error", code=500)

# 管理员：创建违规类型
@violations_bp.route('/admin/types', methods=['POST'])
@jwt_required()
@admin_required
def create_violation_type():
    """创建违规类型（管理员）"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return api_response(message="请求数据为空", status="error", code=400)

        # 检查必填字段
        if 'name' not in data or not data['name']:
            return api_response(message="类型名称不能为空", status="error", code=400)

        # 检查类型名称是否已存在
        existing_type = ViolationType.query.filter_by(name=data['name']).first()
        if existing_type:
            return api_response(message="该违规类型名称已存在", status="error", code=400)

        # 创建违规类型
        schema = ViolationTypeSchema()
        violation_type = schema.load(data)

        # 保存到数据库
        db.session.add(violation_type)
        db.session.commit()

        return api_response(data=schema.dump(violation_type), message="违规类型创建成功", status="success")
    except ValidationError as e:
        return api_response(message=f"数据验证失败: {str(e)}", status="error", code=400)
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建违规类型失败: {str(e)}")
        return api_response(message=f"创建违规类型失败: {str(e)}", status="error", code=500)

# 管理员：更新违规类型
@violations_bp.route('/admin/types/<int:type_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_violation_type(type_id):
    """更新违规类型（管理员）"""
    try:
        # 获取违规类型
        violation_type = ViolationType.query.get(type_id)
        if not violation_type:
            return api_response(message="违规类型不存在", status="error", code=404)

        # 获取请求数据
        data = request.get_json()
        if not data:
            return api_response(message="请求数据为空", status="error", code=400)

        # 检查类型名称是否已存在
        if 'name' in data and data['name'] != violation_type.name:
            existing_type = ViolationType.query.filter_by(name=data['name']).first()
            if existing_type:
                return api_response(message="该违规类型名称已存在", status="error", code=400)

        # 更新违规类型
        for key, value in data.items():
            if hasattr(violation_type, key):
                setattr(violation_type, key, value)

        # 保存到数据库
        db.session.commit()

        # 序列化数据
        schema = ViolationTypeSchema()

        return api_response(data=schema.dump(violation_type), message="违规类型更新成功", status="success")
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新违规类型失败: {str(e)}")
        return api_response(message=f"更新违规类型失败: {str(e)}", status="error", code=500)

# 管理员：删除违规类型
@violations_bp.route('/admin/types/<int:type_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_violation_type(type_id):
    """删除违规类型（管理员）"""
    try:
        # 获取违规类型
        violation_type = ViolationType.query.get(type_id)
        if not violation_type:
            return api_response(message="违规类型不存在", status="error", code=404)

        # 检查是否有违规记录使用了该类型
        records = ViolationRecord.query.filter_by(violation_type_id=type_id).count()
        if records > 0:
            return api_response(message=f"该违规类型已被{records}条记录使用，无法删除", status="error", code=400)

        # 删除违规类型
        db.session.delete(violation_type)
        db.session.commit()

        return api_response(message="违规类型删除成功", status="success")
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除违规类型失败: {str(e)}")
        return api_response(message=f"删除违规类型失败: {str(e)}", status="error", code=500)

# 获取违规统计数据
@violations_bp.route('/admin/statistics', methods=['GET'])
@jwt_required()
@admin_or_security_required
def get_violation_statistics():
    """获取违规统计数据（管理员和保安）"""
    try:
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # 如果没有提供日期范围，默认使用过去一年的数据
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')

        # 构建查询条件
        filters = []
        try:
            start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
            filters.append(ViolationRecord.violation_time >= start_datetime)
        except ValueError:
            current_app.logger.error(f"Invalid start_date format: {start_date}")
            # 使用默认值
            start_datetime = datetime.now() - timedelta(days=365)
            filters.append(ViolationRecord.violation_time >= start_datetime)

        try:
            end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
            end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
            filters.append(ViolationRecord.violation_time <= end_datetime)
        except ValueError:
            current_app.logger.error(f"Invalid end_date format: {end_date}")
            # 使用默认值
            end_datetime = datetime.now().replace(hour=23, minute=59, second=59)
            filters.append(ViolationRecord.violation_time <= end_datetime)

        # 获取总违规数
        total_count = ViolationRecord.query.filter(*filters).count()

        # 获取各状态违规数
        status_counts = db.session.query(
            ViolationRecord.status,
            db.func.count(ViolationRecord.id)
        ).filter(*filters).group_by(ViolationRecord.status).all()

        status_data = {
            0: 0,  # 未处理
            1: 0,  # 已处理
            2: 0,  # 申诉中
            3: 0   # 已撤销
        }
        for status, count in status_counts:
            status_data[status] = count

        # 获取各类型违规数
        type_counts = db.session.query(
            ViolationRecord.violation_type,
            db.func.count(ViolationRecord.id)
        ).filter(*filters).group_by(ViolationRecord.violation_type).all()

        type_data = {}
        for violation_type, count in type_counts:
            type_data[violation_type] = count

        # 获取每月违规数
        # 按月统计
        month_counts = db.session.query(
            db.func.strftime('%Y-%m', ViolationRecord.violation_time),
            db.func.count(ViolationRecord.id)
        ).filter(*filters).group_by(
            db.func.strftime('%Y-%m', ViolationRecord.violation_time)
        ).all()

        month_data = {}
        for month, count in month_counts:
            month_data[month] = count

        # 获取每周违规数
        # 按周统计
        week_counts = db.session.query(
            db.func.strftime('%Y-%W', ViolationRecord.violation_time),
            db.func.count(ViolationRecord.id)
        ).filter(*filters).group_by(
            db.func.strftime('%Y-%W', ViolationRecord.violation_time)
        ).all()

        week_data = {}
        for week, count in week_counts:
            week_data[week] = count

        # 获取每天违规数
        # 按天统计
        day_counts = db.session.query(
            db.func.strftime('%Y-%m-%d', ViolationRecord.violation_time),
            db.func.count(ViolationRecord.id)
        ).filter(*filters).group_by(
            db.func.strftime('%Y-%m-%d', ViolationRecord.violation_time)
        ).all()

        day_data = {}
        for day, count in day_counts:
            day_data[day] = count

        # 获取各地点违规数
        location_counts = db.session.query(
            ViolationRecord.location,
            db.func.count(ViolationRecord.id)
        ).filter(*filters).group_by(ViolationRecord.location).all()

        location_data = {}
        for location, count in location_counts:
            location_data[location] = count

        # 获取申诉统计数据
        appeal_counts = db.session.query(
            Appeal.status,
            db.func.count(Appeal.id)
        ).join(ViolationRecord, Appeal.violation_id == ViolationRecord.id)\
        .filter(*filters).group_by(Appeal.status).all()

        appeal_data = {
            0: 0,  # 待审核
            1: 0,  # 已通过
            2: 0   # 已拒绝
        }
        for status, count in appeal_counts:
            appeal_data[status] = count

        # 构建结果
        result = {
            'total': total_count,
            'status': status_data,
            'types': type_data,
            'months': month_data,
            'weeks': week_data,
            'days': day_data,
            'locations': location_data,
            'appeals': appeal_data
        }

        return api_response(data=result, message="获取违规统计数据成功", status="success")
    except Exception as e:
        current_app.logger.error(f"获取违规统计数据失败: {str(e)}")
        return api_response(message=f"获取违规统计数据失败: {str(e)}", status="error", code=500)
