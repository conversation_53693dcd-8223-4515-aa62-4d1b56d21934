<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>违规API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #409EFF;
            margin-top: 0;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #67C23A;
        }
        .error {
            border-left: 4px solid #F56C6C;
        }
        button {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #337ecc;
        }
        .status {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>违规管理API测试页面</h1>
        <p>此页面用于测试违规管理相关的API接口是否正常工作</p>

        <div class="test-section">
            <h3>1. 测试违规类型选项API</h3>
            <button onclick="testViolationTypeOptions()">测试 /api/violations/type-options</button>
            <div id="typeOptionsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试违规类型列表API</h3>
            <button onclick="testViolationTypes()">测试 /api/violations/types</button>
            <div id="typesResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试公开违规记录API</h3>
            <button onclick="testPublicViolations()">测试 /api/violations/public/records</button>
            <div id="publicResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 测试所有API</h3>
            <button onclick="testAllAPIs()">运行所有测试</button>
        </div>
    </div>

    <script>
        async function testViolationTypeOptions() {
            const resultDiv = document.getElementById('typeOptionsResult');
            resultDiv.innerHTML = '<div class="status">正在测试...</div>';
            
            try {
                const response = await fetch('/api/violations/type-options');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `<div class="status">✅ 成功 (${response.status})</div>${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<div class="status">❌ 失败 (${response.status})</div>${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<div class="status">❌ 网络错误</div>${error.message}`;
            }
        }

        async function testViolationTypes() {
            const resultDiv = document.getElementById('typesResult');
            resultDiv.innerHTML = '<div class="status">正在测试...</div>';
            
            try {
                const response = await fetch('/api/violations/types');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `<div class="status">✅ 成功 (${response.status})</div>${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<div class="status">❌ 失败 (${response.status})</div>${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<div class="status">❌ 网络错误</div>${error.message}`;
            }
        }

        async function testPublicViolations() {
            const resultDiv = document.getElementById('publicResult');
            resultDiv.innerHTML = '<div class="status">正在测试...</div>';
            
            try {
                const response = await fetch('/api/violations/public/records?page=1&per_page=5');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `<div class="status">✅ 成功 (${response.status})</div>${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<div class="status">❌ 失败 (${response.status})</div>${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<div class="status">❌ 网络错误</div>${error.message}`;
            }
        }

        async function testAllAPIs() {
            await testViolationTypeOptions();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testViolationTypes();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testPublicViolations();
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            console.log('页面加载完成，开始API测试');
            testAllAPIs();
        };
    </script>
</body>
</html>
